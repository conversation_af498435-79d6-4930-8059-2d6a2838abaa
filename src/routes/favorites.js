const express = require('express');
const { supabase } = require('../config/supabase');
const { authenticateUser } = require('../middleware/auth');
const router = express.Router();

/**
 * GET /api/favorites
 * Get current user's favorite restaurants
 */
router.get('/', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user's favorites with restaurant details
    const { data: favorites, error } = await supabase
      .from('user_favorites')
      .select(`
        id,
        created_at,
        restaurants (
          id,
          name,
          cuisine_type,
          rating,
          address,
          phone,
          description,
          hours
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Get favorites error:', error);
      return res.status(500).json({ 
        error: 'Failed to get favorites',
        message: error.message 
      });
    }

    // Transform the data to match expected format
    const formattedFavorites = favorites.map(fav => ({
      id: fav.id,
      created_at: fav.created_at,
      restaurant: fav.restaurants
    }));

    res.json({
      success: true,
      favorites: formattedFavorites,
      count: formattedFavorites.length
    });
  } catch (error) {
    console.error('Get favorites error:', error);
    res.status(500).json({ 
      error: 'Failed to get favorites',
      message: 'An error occurred while retrieving favorites' 
    });
  }
});

/**
 * POST /api/favorites
 * Add restaurant to favorites (NEW APPROACH: Store restaurant data when favorited)
 */
router.post('/', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.id;
    const { restaurant_data } = req.body;

    // Validate restaurant data
    if (!restaurant_data) {
      return res.status(400).json({
        error: 'Missing required data',
        message: 'Restaurant information is required'
      });
    }

    // Validate required restaurant fields
    const requiredFields = ['name', 'cuisine_type', 'rating', 'address', 'phone', 'description', 'hours'];
    const missingFields = requiredFields.filter(field => !restaurant_data[field]);

    if (missingFields.length > 0) {
      return res.status(400).json({
        error: 'Invalid restaurant data',
        message: `Restaurant must include: ${missingFields.join(', ')}`
      });
    }

    console.log(`🔖 Adding restaurant to favorites: ${restaurant_data.name}`);

    // ✅ NEW APPROACH: First, store restaurant in database (only when favorited)
    // Check if restaurant already exists
    const { data: existingRestaurant } = await supabase
      .from('restaurants')
      .select('id')
      .eq('name', restaurant_data.name)
      .eq('address', restaurant_data.address)
      .single();

    let restaurant;
    if (existingRestaurant) {
      // Restaurant already exists, use it
      restaurant = existingRestaurant;
      console.log(`✅ Restaurant already exists with ID: ${restaurant.id}`);
    } else {
      // Create new restaurant
      const { data: newRestaurant, error: restaurantError } = await supabase
        .from('restaurants')
        .insert({
          name: restaurant_data.name,
          cuisine_type: restaurant_data.cuisine_type,
          rating: restaurant_data.rating,
          address: restaurant_data.address,
          phone: restaurant_data.phone,
          description: restaurant_data.description,
          hours: restaurant_data.hours,
          gemini_response: restaurant_data,
        })
        .select()
        .single();

      if (restaurantError) {
        console.error('Restaurant storage error:', restaurantError);
        return res.status(500).json({
          error: 'Failed to store restaurant',
          message: restaurantError.message
        });
      }

      restaurant = newRestaurant;
      console.log(`✅ New restaurant created with ID: ${restaurant.id}`);
    }

    // ✅ Then, create favorite relationship
    const { data: favorite, error: favoriteError } = await supabase
      .from('user_favorites')
      .insert({
        user_id: userId,
        restaurant_id: restaurant.id
      })
      .select()
      .single();

    if (favoriteError) {
      if (favoriteError.code === '23505') { // Unique constraint violation
        return res.status(409).json({
          error: 'Already favorited',
          message: 'This restaurant is already in your favorites'
        });
      }
      console.error('Add favorite error:', favoriteError);
      return res.status(500).json({
        error: 'Failed to add favorite',
        message: favoriteError.message
      });
    }

    console.log(`✅ Favorite created with ID: ${favorite.id}`);

    res.status(201).json({
      success: true,
      message: 'Restaurant added to favorites',
      favorite_id: favorite.id,
      restaurant_id: restaurant.id
    });
  } catch (error) {
    console.error('Add favorite error:', error);
    res.status(500).json({ 
      error: 'Failed to add favorite',
      message: 'An error occurred while adding to favorites' 
    });
  }
});

/**
 * DELETE /api/favorites/:restaurantId
 * Remove restaurant from favorites
 */
router.delete('/:restaurantId', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.id;
    const { restaurantId } = req.params;

    const { data: deleted, error } = await supabase
      .from('user_favorites')
      .delete()
      .eq('user_id', userId)
      .eq('restaurant_id', restaurantId)
      .select();

    if (error) {
      console.error('Remove favorite error:', error);
      return res.status(500).json({ 
        error: 'Failed to remove favorite',
        message: error.message 
      });
    }

    if (!deleted || deleted.length === 0) {
      return res.status(404).json({ 
        error: 'Favorite not found',
        message: 'This restaurant is not in your favorites' 
      });
    }

    res.json({
      success: true,
      message: 'Restaurant removed from favorites'
    });
  } catch (error) {
    console.error('Remove favorite error:', error);
    res.status(500).json({ 
      error: 'Failed to remove favorite',
      message: 'An error occurred while removing from favorites' 
    });
  }
});

module.exports = router;
