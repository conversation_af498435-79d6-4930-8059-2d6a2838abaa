const express = require('express');
const { supabase } = require('../config/supabase');
const router = express.Router();

/**
 * POST /api/auth/google
 * Handle Google OAuth login
 */
router.post('/google', async (req, res) => {
  try {
    const { idToken } = req.body;

    if (!idToken) {
      return res.status(400).json({ 
        error: 'Missing required field',
        message: 'Google ID token is required' 
      });
    }

    // Sign in with Google using the ID token
    const { data, error } = await supabase.auth.signInWithIdToken({
      provider: 'google',
      token: idToken
    });

    if (error) {
      console.error('Google auth error:', error);
      return res.status(400).json({ 
        error: 'Authentication failed',
        message: error.message 
      });
    }

    res.json({
      success: true,
      user: data.user,
      session: data.session
    });
  } catch (error) {
    console.error('Google login error:', error);
    res.status(500).json({ 
      error: 'Lo<PERSON> failed',
      message: 'An error occurred during Google login' 
    });
  }
});

/**
 * POST /api/auth/guest
 * Create anonymous guest user
 */
router.post('/guest', async (req, res) => {
  try {
    // Sign in anonymously
    const { data, error } = await supabase.auth.signInAnonymously();

    if (error) {
      console.error('Anonymous auth error:', error);
      return res.status(400).json({ 
        error: 'Guest login failed',
        message: error.message 
      });
    }

    res.json({
      success: true,
      user: data.user,
      session: data.session,
      isGuest: true
    });
  } catch (error) {
    console.error('Guest login error:', error);
    res.status(500).json({ 
      error: 'Guest login failed',
      message: 'An error occurred during guest login' 
    });
  }
});

/**
 * POST /api/auth/logout
 * Logout user (clear session)
 */
router.post('/logout', async (req, res) => {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error('Logout error:', error);
      return res.status(400).json({ 
        error: 'Logout failed',
        message: error.message 
      });
    }

    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ 
      error: 'Logout failed',
      message: 'An error occurred during logout' 
    });
  }
});

/**
 * POST /api/auth/refresh
 * Refresh user session
 */
router.post('/refresh', async (req, res) => {
  try {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      return res.status(400).json({
        error: 'Missing token',
        message: 'Refresh token is required'
      });
    }

    const { data, error } = await supabase.auth.refreshSession({
      refresh_token
    });

    if (error) {
      return res.status(401).json({
        error: 'Token refresh failed',
        message: error.message
      });
    }

    res.json({
      success: true,
      session: {
        access_token: data.session.access_token,
        refresh_token: data.session.refresh_token,
        expires_at: data.session.expires_at
      }
    });

  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(500).json({
      error: 'Token refresh failed',
      message: 'An error occurred during token refresh'
    });
  }
});

/**
 * GET /api/auth/user
 * Get current user info from session
 */
router.get('/user', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        error: 'Authentication required',
        message: 'Please provide a valid authorization token' 
      });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({ 
        error: 'Invalid token',
        message: 'The provided token is invalid or expired' 
      });
    }

    res.json({
      success: true,
      user: user,
      isGuest: user.is_anonymous || false
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ 
      error: 'Failed to get user info',
      message: 'An error occurred while retrieving user information' 
    });
  }
});

module.exports = router;
