const express = require("express");
const { supabase } = require("../config/supabase");
const { optionalAuth } = require("../middleware/auth");
const GeminiService = require("../services/GeminiService");
const router = express.Router();

/**
 * GET /api/restaurants/search
 * Search restaurants via Gemini AI
 */
router.get("/search", optionalAuth, async (req, res) => {
  try {
    const { q: query, location } = req.query;

    if (!query) {
      return res.status(400).json({
        error: "Missing required parameter",
        message: 'Query parameter "q" is required',
      });
    }

    // Use Gemini service to search for restaurants
    const geminiService = new GeminiService();

    try {
      const restaurants = await geminiService.searchRestaurants(query, location);

      // ✅ NEW APPROACH: Return results directly without caching
      // Restaurants will only be stored in database when favorited by users
      console.log(`🔍 Search returned ${restaurants.length} restaurants (not cached)`);

      res.json({
        success: true,
        restaurants: restaurants,
        count: restaurants.length,
      });

    } catch (geminiError) {
      // Check if this is a rate limit error from Gemini service
      if (geminiError.isRateLimit && geminiError.statusCode === 429) {
        console.log("🚫 Rate limit exceeded, returning 429 to client");

        // Convert retry-after to human readable format
        const retryAfterSeconds = parseInt(geminiError.retryAfter) || 60;
        const retryAfterText = retryAfterSeconds < 60
          ? `${retryAfterSeconds} seconds`
          : `${Math.ceil(retryAfterSeconds / 60)} minutes`;

        return res.status(429).json({
          error: "Rate limit exceeded",
          message: "API rate limit reached. Please try again later.",
          status_code: 429,
          retry_after: retryAfterText,
          retry_after_seconds: retryAfterSeconds,
          suggestion: "You can check current rate limits at /api/restaurants/rate-limit"
        });
      }

      // For other Gemini errors, re-throw to be handled by outer catch
      throw geminiError;
    }

  } catch (error) {
    console.error("Restaurant search error:", error);
    res.status(500).json({
      error: "Search failed",
      message: "An error occurred while searching for restaurants",
    });
  }
});

/**
 * GET /api/restaurants/trending
 * Get popular/trending restaurants
 */
router.get("/trending", optionalAuth, async (req, res) => {
  try {
    // ✅ NEW APPROACH: Get most favorited restaurants (real user engagement)
    // Only restaurants that have been favorited by users will appear here
    const { data: restaurants, error } = await supabase
      .from("restaurants")
      .select(`
        *,
        user_favorites(count)
      `)
      .order("created_at", { ascending: false })
      .limit(20);

    if (error) {
      console.error("Get trending restaurants error:", error);
      return res.status(500).json({
        error: "Failed to get trending restaurants",
        message: error.message,
      });
    }

    // Add favorite count to each restaurant and sort by popularity
    const restaurantsWithCount = restaurants?.map(restaurant => ({
      ...restaurant,
      favorite_count: restaurant.user_favorites?.length || 0,
      user_favorites: undefined // Remove the raw count data from response
    })) || [];

    // Sort by favorite count (most favorited first), then by rating
    restaurantsWithCount.sort((a, b) => {
      if (b.favorite_count !== a.favorite_count) {
        return b.favorite_count - a.favorite_count;
      }
      return b.rating - a.rating;
    });

    res.json({
      success: true,
      restaurants: restaurantsWithCount,
      count: restaurantsWithCount.length,
      message: restaurantsWithCount.length === 0
        ? "No trending restaurants yet. Restaurants appear here when users add them to favorites."
        : `Showing ${restaurantsWithCount.length} trending restaurants based on user favorites`
    });
  } catch (error) {
    console.error("Get trending restaurants error:", error);
    res.status(500).json({
      error: "Failed to get trending restaurants",
      message: "An error occurred while retrieving trending restaurants",
    });
  }
});

/**
 * GET /api/restaurants/api-status
 * Check Google Gemini API status and configuration
 */
router.get("/api-status", async (req, res) => {
  try {
    const apiKey = process.env.GEMINI_API_KEY;

    if (!apiKey) {
      return res.status(500).json({
        error: "API key not configured",
        message: "Google Gemini API key is not set in environment variables",
        status: "NOT_CONFIGURED"
      });
    }

    // Test API connectivity with a simple request
    const { GoogleGenAI } = require("@google/genai");
    const client = new GoogleGenAI({ apiKey: apiKey });

    try {
      // Make a minimal test request to check API status
      await client.models.generateContent({
        model: "gemini-2.0-flash-lite",
        contents: "Hello",
        config: {
          maxOutputTokens: 10,
          thinkingConfig: {
            thinkingBudget: 0
          }
        }
      });

      res.json({
        success: true,
        api_status: {
          provider: "Google Gemini API",
          model: "gemini-2.0-flash-lite",
          status: "CONNECTED",
          last_test: new Date().toISOString()
        },
        rate_limits: {
          free_tier: {
            requests_per_minute: 15,
            requests_per_day: 1500,
            tokens_per_minute: 32000,
            tokens_per_day: 50000
          },
          note: "Actual limits may vary based on your Google AI Studio quota"
        },
        message: "Google Gemini API is working correctly"
      });

    } catch (testError) {
      console.error("Gemini API test error:", testError);

      // Check for specific error types
      if (testError.status === 429 || (testError.message && testError.message.includes('quota'))) {
        return res.status(429).json({
          error: "Rate limit exceeded",
          message: "Google Gemini API rate limit reached. Please try again later.",
          status: "RATE_LIMITED",
          retry_after: "60 seconds"
        });
      }

      if (testError.status === 401 || testError.status === 403) {
        return res.status(401).json({
          error: "Invalid API key",
          message: "The Google Gemini API key is invalid or expired",
          status: "UNAUTHORIZED"
        });
      }

      return res.status(500).json({
        error: "API connection failed",
        message: "Unable to connect to Google Gemini API",
        status: "CONNECTION_ERROR",
        details: testError.message
      });
    }

  } catch (error) {
    console.error("API status check error:", error);
    res.status(500).json({
      error: "Failed to check API status",
      message: "An error occurred while checking Google Gemini API status",
      status: "ERROR"
    });
  }
});

/**
 * GET /api/restaurants/:id
 * Get specific restaurant details
 */
router.get("/:id", optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const { data: restaurant, error } = await supabase
      .from("restaurants")
      .select("*")
      .eq("id", id)
      .single();

    if (error || !restaurant) {
      return res.status(404).json({
        error: "Restaurant not found",
        message: "The requested restaurant could not be found",
      });
    }

    res.json({
      success: true,
      restaurant: restaurant,
    });
  } catch (error) {
    console.error("Get restaurant error:", error);
    res.status(500).json({
      error: "Failed to get restaurant",
      message: "An error occurred while retrieving restaurant details",
    });
  }
});

module.exports = router;
