const { GoogleGenAI } = require("@google/genai");

class GeminiService {
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY;
    this.model = "gemini-2.0-flash-lite";

    if (!this.apiKey) {
      console.warn("⚠️  Google Gemini API key not found. Using mock data only.");
      this.client = null;
    } else {
      this.client = new GoogleGenAI({ apiKey: this.apiKey });
      console.log("✅ Google Gemini API client initialized");
    }
  }

  /**
   * Search for restaurants using Gemini AI
   * @param {string} query - Search query
   * @param {string} location - Location (optional)
   * @returns {Array} Array of restaurant objects
   */
  async searchRestaurants(query, location = "") {
    try {
      if (!this.client) {
        console.warn("Google Gemini API client not available, returning mock data");
        return this.getMockRestaurants(query, location);
      }

      const prompt = this.buildSearchPrompt(query, location);

      console.log(
        "🤖 Calling Google Gemini API for query:",
        query,
        "location:",
        location
      );

      const response = await this.client.models.generateContent({
        model: this.model,
        contents: prompt,
        config: {
          temperature: 0.7,
          maxOutputTokens: 2000,
          // Disable thinking for faster responses and lower token usage
          thinkingConfig: {
            thinkingBudget: 0
          }
        }
      });

      const aiResponse = response.text;
      console.log("🎯 Google Gemini AI Response:", aiResponse);

      // Parse the JSON response from Gemini
      try {
        // Clean the response - remove markdown code blocks if present
        let cleanResponse = aiResponse.trim();
        if (cleanResponse.startsWith("```json")) {
          cleanResponse = cleanResponse
            .replace(/```json\s*/, "")
            .replace(/```\s*$/, "");
        } else if (cleanResponse.startsWith("```")) {
          cleanResponse = cleanResponse
            .replace(/```\s*/, "")
            .replace(/```\s*$/, "");
        }

        console.log("🧹 Cleaned AI Response:", cleanResponse);

        const restaurants = JSON.parse(cleanResponse);
        return Array.isArray(restaurants) ? restaurants : [restaurants];
      } catch (parseError) {
        console.error("Failed to parse Gemini response as JSON:", parseError);
        console.log("Raw response:", aiResponse);
        // Fallback to mock data if parsing fails
        return this.getMockRestaurants(query, location);
      }
    } catch (error) {
      console.error("Google Gemini API error:", error);

      // Check if this is a rate limit error (429 or quota exceeded)
      if (error.status === 429 || (error.message && error.message.includes('quota'))) {
        // Extract retry-after information if available
        const retryAfter = error.retryAfter || '60'; // Default to 60 seconds

        // Throw a specific rate limit error instead of falling back
        const rateLimitError = new Error('Rate limit exceeded');
        rateLimitError.statusCode = 429;
        rateLimitError.retryAfter = retryAfter;
        rateLimitError.isRateLimit = true;
        throw rateLimitError;
      }

      // For other errors (network, auth, parsing, etc.), fallback to mock data
      console.log("🎭 Falling back to mock data due to non-rate-limit error");
      return this.getMockRestaurants(query, location);
    }
  }

  /**
   * Build structured prompt for Gemini AI
   * @param {string} query - Search query
   * @param {string} location - Location
   * @returns {string} Formatted prompt
   */
  buildSearchPrompt(query, location) {
    return `
You are a restaurant recommendation AI. Based on the search query "${query}" ${
      location ? `in ${location}` : ""
    }, 
provide a JSON array of 5-10 restaurant recommendations with the following exact structure:

[
  {
    "name": "Restaurant Name",
    "cuisine_type": "Italian",
    "rating": 4.5,
    "address": "123 Main St, City, State 12345",
    "phone": "(*************",
    "description": "A cozy Italian restaurant known for its authentic pasta dishes and warm atmosphere. Features traditional recipes passed down through generations with fresh, locally-sourced ingredients.",
    "hours": {
      "monday": "11:00 AM - 10:00 PM",
      "tuesday": "11:00 AM - 10:00 PM",
      "wednesday": "11:00 AM - 10:00 PM",
      "thursday": "11:00 AM - 10:00 PM",
      "friday": "11:00 AM - 11:00 PM",
      "saturday": "11:00 AM - 11:00 PM",
      "sunday": "12:00 PM - 9:00 PM"
    }
  }
]

Requirements:
- Rating should be between 3.0 and 5.0
- Include realistic addresses and phone numbers
- Provide varied cuisine types
- Include operating hours for all days
- Add a compelling 1-2 sentence description highlighting what makes each restaurant special
- Return only valid JSON, no additional text
`;
  }

  /**
   * Get mock restaurant data for testing
   * @param {string} query - Search query
   * @param {string} location - Location
   * @returns {Array} Mock restaurant data
   */
  getMockRestaurants(query, location) {
    const mockRestaurants = [
      {
        name: "Bella Vista Italian",
        cuisine_type: "Italian",
        rating: 4.5,
        address: "123 Main Street, Downtown, NY 10001",
        phone: "(*************",
        description:
          "A cozy Italian restaurant known for its authentic pasta dishes and warm atmosphere. Features traditional recipes passed down through generations with fresh, locally-sourced ingredients.",
        hours: {
          monday: "11:00 AM - 10:00 PM",
          tuesday: "11:00 AM - 10:00 PM",
          wednesday: "11:00 AM - 10:00 PM",
          thursday: "11:00 AM - 10:00 PM",
          friday: "11:00 AM - 11:00 PM",
          saturday: "11:00 AM - 11:00 PM",
          sunday: "12:00 PM - 9:00 PM",
        },
      },
      {
        name: "Sakura Sushi Bar",
        cuisine_type: "Japanese",
        rating: 4.7,
        address: "456 Oak Avenue, Midtown, NY 10002",
        phone: "(*************",
        description:
          "An elegant sushi bar offering the freshest fish and expertly crafted rolls. Known for its omakase experience and skilled sushi chefs who create artful presentations.",
        hours: {
          monday: "5:00 PM - 11:00 PM",
          tuesday: "5:00 PM - 11:00 PM",
          wednesday: "5:00 PM - 11:00 PM",
          thursday: "5:00 PM - 11:00 PM",
          friday: "5:00 PM - 12:00 AM",
          saturday: "5:00 PM - 12:00 AM",
          sunday: "5:00 PM - 10:00 PM",
        },
      },
      {
        name: "The Burger Joint",
        cuisine_type: "American",
        rating: 4.2,
        address: "789 Elm Street, Uptown, NY 10003",
        phone: "(*************",
        description:
          "A casual burger spot famous for its juicy, handcrafted burgers made with premium beef and creative toppings. Features crispy fries and thick milkshakes in a retro diner atmosphere.",
        hours: {
          monday: "11:00 AM - 9:00 PM",
          tuesday: "11:00 AM - 9:00 PM",
          wednesday: "11:00 AM - 9:00 PM",
          thursday: "11:00 AM - 9:00 PM",
          friday: "11:00 AM - 10:00 PM",
          saturday: "11:00 AM - 10:00 PM",
          sunday: "12:00 PM - 8:00 PM",
        },
      },
    ];

    // Filter based on query (simple keyword matching)
    const filtered = mockRestaurants.filter(
      (restaurant) =>
        restaurant.name.toLowerCase().includes(query.toLowerCase()) ||
        restaurant.cuisine_type.toLowerCase().includes(query.toLowerCase())
    );

    return filtered.length > 0 ? filtered : mockRestaurants;
  }
}

module.exports = GeminiService;
