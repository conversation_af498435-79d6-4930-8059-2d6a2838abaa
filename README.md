# Foodie Android App Backend

A Node.js/Express backend API for the Foodie Android app with Supabase integration, Google OAuth authentication, and AI-powered restaurant recommendations via Gemini.

## 🚀 Features

- **Simplified Authentication**: Google OAuth + Anonymous guest access
- **Restaurant Search**: AI-powered recommendations using Gemini 2.0 via OperRouter
- **Favorites Management**: User-specific restaurant favorites with RLS
- **Supabase Integration**: PostgreSQL database with real-time capabilities
- **RESTful API**: Clean, documented endpoints for Android app integration
- **Security**: Row Level Security (RLS) policies and proper authentication middleware

## 📋 Prerequisites

- Node.js 20+ (recommended)
- Supabase account and project
- Google OAuth credentials (for authentication)
- OperRouter API key (for Gemini AI integration)

## 🛠️ Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <your-repo-url>
cd Backend
npm install
```

### 2. Configure Supabase API Keys

**IMPORTANT**: You need to get your actual Supabase API keys to make the backend fully functional.

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: `taz` (ID: ivjxddvsjtaaavkovvyq)
3. Navigate to **Settings** → **API**
4. Copy the following keys:
   - **Project URL**: `https://ivjxddvsjtaaavkovvyq.supabase.co`
   - **anon public key**: Starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - **service_role secret key**: Starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### 3. Update Environment Variables

Edit the `.env` file and replace the placeholder values:

```env
# Supabase Configuration
SUPABASE_URL=https://ivjxddvsjtaaavkovvyq.supabase.co
SUPABASE_ANON_KEY=your-actual-anon-key-from-supabase-dashboard
SUPABASE_SERVICE_ROLE_KEY=your-actual-service-role-key-from-supabase-dashboard

# Google OAuth Configuration (get from Google Cloud Console)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Google Gemini API Configuration (Direct from Google)
GEMINI_API_KEY=your-google-gemini-api-key

# Server Configuration
PORT=3000
NODE_ENV=development

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000
```

### 4. Database Setup

The database schema has already been created with the following tables:
- ✅ `restaurants` - Restaurant data cache with Gemini responses
- ✅ `user_favorites` - User favorite restaurants (many-to-many)
- ✅ Row Level Security (RLS) policies configured

### 5. Start the Server

```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:3000`

## 📚 API Endpoints

### Health Check
```
GET /health
```

### Authentication
```
POST /api/auth/google          # Google OAuth login
POST /api/auth/guest           # Anonymous guest login
POST /api/auth/refresh         # Refresh session token
POST /api/auth/logout          # Logout user
GET  /api/auth/user            # Get current user info
```

### Restaurants
```
GET /api/restaurants/search?q=italian&location=toronto    # Search restaurants
GET /api/restaurants/trending                             # Get trending restaurants
GET /api/restaurants/api-status                           # Check Google Gemini API status
GET /api/restaurants/:id                                  # Get restaurant details
```

### Favorites (Requires Authentication)
```
GET    /api/favorites                    # Get user's favorites
POST   /api/favorites                    # Add restaurant to favorites
DELETE /api/favorites/:restaurantId      # Remove from favorites
```

## 🧪 Testing the API

### Test Health Endpoint
```bash
curl http://localhost:3000/health
```

### Test Restaurant Search
```bash
curl "http://localhost:3000/api/restaurants/search?q=italian"
```

### Test with Authentication
```bash
# First get a token from Google OAuth or guest login
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3000/api/favorites
```

## 🔧 Current Status

### ✅ Completed
- [x] Supabase database schema created with descriptions
- [x] Row Level Security (RLS) policies implemented
- [x] Express.js server structure
- [x] Authentication middleware
- [x] Restaurant search with AI-generated descriptions
- [x] Favorites management endpoints
- [x] Error handling and logging
- [x] TypeScript types generated
- [x] Image URLs removed (not working properly)
- [x] Restaurant descriptions added for better UX

### ⚠️ Needs Configuration
- [ ] **Supabase API Keys** - Replace placeholder keys with real ones
- [ ] **Google OAuth Setup** - Configure Google Cloud Console
- [ ] **OperRouter Integration** - Get API key for Gemini AI
- [ ] **Production Deployment** - Deploy to Railway/Render

## 🔐 Security Features

- **Row Level Security**: Users can only access their own favorites
- **API Key Authentication**: Supabase handles user sessions
- **CORS Protection**: Configurable allowed origins
- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Input Validation**: Request validation and sanitization

## 🚀 Next Steps

1. **Get Supabase API Keys**: Update `.env` with real keys from dashboard
2. **Configure Google OAuth**: Set up Google Cloud Console project
3. **Test Authentication**: Verify Google login and guest access work
4. **Integrate Real AI**: Connect to OperRouter for Gemini recommendations
5. **Deploy**: Deploy to Railway or Render for production use

## 📱 Android Integration

The backend is designed to work with your Android app. Key integration points:

- **Base URL**: `http://localhost:3000` (development) or your deployed URL
- **Authentication**: Use Supabase Android SDK for seamless auth
- **API Format**: All responses follow `{success: boolean, data: any, error?: string}` pattern

## 🐛 Troubleshooting

### "Invalid API key" Error
- Make sure you've replaced the placeholder Supabase keys with real ones from your dashboard

### Server Won't Start
- Check that port 3000 is available
- Verify all required environment variables are set

### Database Connection Issues
- Confirm your Supabase project is active and healthy
- Check that RLS policies allow your operations

## 📞 Support

If you encounter issues:
1. Check the server logs for detailed error messages
2. Verify all environment variables are correctly set
3. Test individual endpoints with curl to isolate issues
4. Check Supabase dashboard for database connectivity
