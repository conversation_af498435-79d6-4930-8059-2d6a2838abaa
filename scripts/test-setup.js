#!/usr/bin/env node

/**
 * Test script to verify <PERSON><PERSON> Backend setup
 * Run with: node scripts/test-setup.js
 */

require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testSetup() {
  console.log('🧪 Testing Foodie Backend Setup...\n');

  // Test 1: Environment Variables
  console.log('1️⃣ Checking Environment Variables:');
  const requiredEnvVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'PORT'
  ];

  let envIssues = 0;
  requiredEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      console.log(`   ✅ ${envVar}: Set`);
    } else {
      console.log(`   ❌ ${envVar}: Missing`);
      envIssues++;
    }
  });

  const optionalEnvVars = [
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'OPERROUTER_API_KEY'
  ];

  console.log('\n   Optional (for full functionality):');
  optionalEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      console.log(`   ✅ ${envVar}: Set`);
    } else {
      console.log(`   ⚠️  ${envVar}: Not set (will use mock data)`);
    }
  });

  // Test 2: Server Health
  console.log('\n2️⃣ Testing Server Health:');
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    if (response.status === 200) {
      console.log('   ✅ Server is running and healthy');
      console.log(`   📊 Environment: ${response.data.environment}`);
    }
  } catch (error) {
    console.log('   ❌ Server health check failed');
    console.log('   💡 Make sure the server is running: npm start');
    return;
  }

  // Test 3: Restaurant Search (Mock Data)
  console.log('\n3️⃣ Testing Restaurant Search:');
  try {
    const response = await axios.get(`${BASE_URL}/api/restaurants/search?q=italian`);
    if (response.data.success && response.data.restaurants.length > 0) {
      console.log('   ✅ Restaurant search working with mock data');
      console.log(`   🍝 Found ${response.data.count} restaurants`);
    }
  } catch (error) {
    console.log('   ❌ Restaurant search failed');
    console.log(`   Error: ${error.response?.data?.message || error.message}`);
  }

  // Test 4: Database Connection
  console.log('\n4️⃣ Testing Database Connection:');
  try {
    const response = await axios.get(`${BASE_URL}/api/restaurants/trending`);
    if (response.data.success) {
      console.log('   ✅ Database connection working');
      console.log(`   📈 Found ${response.data.count} trending restaurants`);
    }
  } catch (error) {
    if (error.response?.data?.message?.includes('Invalid API key')) {
      console.log('   ⚠️  Database connection needs real Supabase API keys');
      console.log('   💡 Update SUPABASE_ANON_KEY in .env with real key from dashboard');
    } else {
      console.log('   ❌ Database connection failed');
      console.log(`   Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // Summary
  console.log('\n📋 Setup Summary:');
  if (envIssues === 0) {
    console.log('✅ Basic setup complete! Server is running with mock data.');
    console.log('\n🔧 Next Steps:');
    console.log('1. Get real Supabase API keys from your dashboard');
    console.log('2. Configure Google OAuth credentials');
    console.log('3. Get OperRouter API key for Gemini AI');
    console.log('4. Test authentication endpoints');
  } else {
    console.log('❌ Setup incomplete. Please fix environment variable issues.');
  }

  console.log('\n📚 For detailed setup instructions, see README.md');
}

// Run the test
testSetup().catch(console.error);
