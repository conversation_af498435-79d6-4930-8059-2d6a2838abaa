{"name": "foodie-backend", "version": "1.0.0", "description": "Backend API for Foodie Android App with Supabase integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "keywords": ["foodie", "restaurant", "api", "supabase", "gemini"], "author": "", "license": "MIT", "dependencies": {"@google/genai": "^1.12.0", "@supabase/supabase-js": "^2.39.0", "axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}}