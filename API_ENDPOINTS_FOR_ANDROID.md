# Foodie Backend API Endpoints for Android

## Base URL
```
http://10.0.0.169:3000/api
```

## Authentication

### 1. Google OAuth Login
```
POST /auth/google
Content-Type: application/json

{
  "idToken": "your-google-id-token"
}

Response:
{
  "success": true,
  "user": { ... },
  "session": {
    "access_token": "jwt-token",
    "refresh_token": "refresh-token",
    "expires_in": 3600
  }
}
```

### 2. Get Current User
```
GET /auth/user
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "user": { ... },
  "isGuest": false
}
```

### 3. Guest Login (Anonymous)
```
POST /auth/guest

Response:
{
  "success": true,
  "user": { ... },
  "session": { ... },
  "isGuest": true
}
```

## Restaurant Search

### Search Restaurants
```
GET /restaurants/search?q={query}&location={location}
Authorization: Bearer {access_token} (optional)

Example:
GET /restaurants/search?q=pizza&location=Toronto

Response:
{
  "success": true,
  "restaurants": [
    {
      "name": "Pizzeria Libretto",
      "cuisine_type": "Pizza",
      "rating": 4.6,
      "address": "221 Ossington Ave, Toronto, ON M6J 2Z8",
      "phone": "(*************",
      "description": "...",
      "hours": { ... }
    }
  ],
  "count": 6
}
```

## Favorites Management

### Get User Favorites
```
GET /favorites
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "favorites": [
    {
      "id": "favorite-id",
      "created_at": "2025-08-04T23:12:08.92365Z",
      "restaurant": { ... }
    }
  ],
  "count": 1
}
```

### Add Restaurant to Favorites
```
POST /favorites
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "restaurant_data": {
    "name": "Pizzeria Libretto",
    "cuisine_type": "Pizza",
    "rating": 4.6,
    "address": "221 Ossington Ave, Toronto, ON M6J 2Z8",
    "phone": "(*************",
    "description": "...",
    "hours": { ... }
  }
}

Response:
{
  "success": true,
  "favorite": {
    "id": "favorite-id",
    "restaurant": { ... }
  }
}
```

### Remove from Favorites
```
DELETE /favorites/{favorite_id}
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "message": "Restaurant removed from favorites"
}
```

## Error Handling

All endpoints return errors in this format:
```json
{
  "error": "Error Type",
  "message": "Detailed error message"
}
```

Common HTTP status codes:
- 200: Success
- 400: Bad Request (missing parameters, invalid data)
- 401: Unauthorized (invalid or missing token)
- 404: Not Found
- 500: Internal Server Error

## Authentication Flow for Android

1. **User signs in with Google** → Get Google ID token
2. **Send ID token to backend** → `POST /auth/google`
3. **Store session data** → Save access_token, refresh_token, user info
4. **Use access_token** → Include in Authorization header for all API calls
5. **Handle token expiry** → Refresh token or re-authenticate

## Example Android Integration

```kotlin
// 1. After Google Sign-In
val idToken = googleSignInAccount.idToken
val authRequest = GoogleAuthRequest(idToken)

// 2. Send to backend
val response = apiService.authenticateWithGoogle(authRequest)

// 3. Store session
sessionManager.saveSession(response.session)
sessionManager.saveUser(response.user)

// 4. Use for API calls
val authHeader = "Bearer ${sessionManager.getAccessToken()}"
val restaurants = apiService.searchRestaurants("pizza", "Toronto", authHeader)
```

## Testing

Your backend is now fully functional! Test these endpoints with your Android app using the session data from successful Google authentication.
