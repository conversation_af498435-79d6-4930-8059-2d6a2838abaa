# Foodie Android App Backend Implementation Plan

## 1. Technology Stack Recommendation

### Primary Stack: Node.js + Express + Supabase
- **Backend Framework**: Express.js for business logic and AI integration
- **Database**: Supabase PostgreSQL with built-in APIs and real-time features
- **Authentication**: Simplified Supabase Auth with only:
  - Google OAuth login (primary authentication method)
  - Anonymous guest users (for browsing without account)
- **AI Integration**: Gemini 2.0 via OperRouter for restaurant recommendations
- **Hosting**: Supabase for database/auth + Railway/Render for Express API

### Alternative Considerations
- **Full Supabase**: Use Supabase Edge Functions instead of Express.js
- **Hybrid Approach**: Direct Supabase calls for CRUD + Express for AI logic
## 2. Supabase Database Schema Design

### Core Tables

#### users (Managed by Supabase Auth)
- **id**: UUID (auto-generated by Supabase Auth)
- **email**: String (nullable for anonymous users)
- **provider**: String (google, anonymous)
- **created_at**: Timestamp
- **updated_at**: Timestamp

#### restaurants (Restaurant data cache with Gemini response storage)
- **id**: UUID (primary key)
- **name**: String
- **cuisine_type**: String
- **rating**: Float
- **address**: String
- **phone**: String
- **hours**: JSONB
- **gemini_response**: JSONB (raw AI response)
- **image_urls**: String[]
- **created_at**: Timestamp

#### user_favorites (Many-to-many relationship for user favorites)
- **id**: UUID (primary key)
- **user_id**: UUID (foreign key to auth.users)
- **restaurant_id**: UUID (foreign key to restaurants)
- **created_at**: Timestamp

### Key Design Decisions
- **Leverage Supabase Auth**: Built-in user management with multiple providers
- **Row Level Security (RLS)**: Automatic data access control
- **JSONB fields**: Flexible storage for Gemini API responses
- **Simple Schema**: Focus on core features only
- **Foreign key constraints**: Data integrity with cascade deletes
## 3. API Endpoint Specifications

### Authentication Endpoints (Simplified Supabase Auth)
- **POST /api/auth/google** - Login with Google OAuth
- **POST /api/auth/guest** - Create anonymous guest user via Supabase
- **POST /api/auth/logout** - Logout user (clear session)
- **GET /api/auth/user** - Get current user info from session

### Restaurant Search Endpoints
- **GET /api/restaurants/search** - Search restaurants via Gemini
  - Query params: `q={query}&location={location}`
- **GET /api/restaurants/:id** - Get specific restaurant details
- **GET /api/restaurants/trending** - Get popular restaurants

### Favorites Management Endpoints (Session-based Authentication)
- **GET /api/favorites** - Get current user's favorite restaurants
- **POST /api/favorites** - Add restaurant to favorites (requires auth)
- **DELETE /api/favorites/:restaurantId** - Remove from favorites (requires auth)
### Data Compatibility
- **Maintain exact JSON structure** expected by Android app
- **Map Gemini responses** to existing restaurant card format
- **Preserve rating, reviews, cuisine, and image data structure**

### Authentication Integration
- **Session-based authentication** via Supabase (no JWT complexity)
- **Two login methods only**: Google OAuth and Anonymous guest access
- **Seamless user experience** with automatic session management
- **Guest to Google account transition** support
## 4. Gemini 2.0 Integration Strategy

### OperRouter Integration
- **Use OperRouter's free tier** for Gemini 2.0 access
- **Implement structured prompts** for consistent restaurant data
- **Build fallback system** for API failures
- **Cache responses** in Supabase to reduce API calls

### Prompt Engineering
- **Design prompts** to return restaurant data in Android-compatible format
- **Include rating distributions** for detail screen progress bars
- **Generate realistic data**: addresses, hours, and contact information
- **Ensure cuisine type consistency** with app's existing categories

### Response Processing
- **Parse and validate** Gemini JSON responses
- **Store raw responses** for future analysis
- **Implement data enrichment** for missing fields
- **Handle API rate limits** and errors gracefully
## 5. Data Flow Architecture

### Authentication Flow
#### Google Login Flow:
- Android app initiates Google Sign-In via backend API
- Backend handles Google OAuth and creates session
- Android stores session info for subsequent requests

#### Guest User Flow:
- Android app requests anonymous user creation
- Backend creates anonymous user via Supabase Auth
- Session automatically managed by Supabase
- All requests include session authentication

### Search Flow
1. Android app sends search query with session
2. Backend validates session and extracts user info
3. Backend queries Gemini 2.0 via OperRouter
4. Process and validate Gemini response
5. Store restaurants in Supabase database
6. Return formatted data to Android app
7. Android app displays results using existing UI

### Favorites Flow (Authenticated Users Only)
1. User taps favorite button in Android app
2. Android sends request with session and restaurant data
3. Backend validates session and user permissions
4. Backend stores/removes favorite relationship in Supabase
5. Return updated favorites list
6. Android app updates UI state

### User Transition Flow
- **Anonymous users** can upgrade to registered accounts
- **Favorites and data** are preserved during transition
- **Seamless experience** with no data loss
## 6. Backend Service Architecture

### Service Layer Structure
- **SupabaseAuthService**: Handle Supabase authentication integration
- **GeminiService**: Handle AI API integration and response processing
- **RestaurantService**: Business logic for restaurant data management
- **FavoritesService**: User favorites management with RLS

### Middleware Components
- **Session Authentication**: Validate Supabase sessions (simplified)
- **Authorization**: Check user permissions and RLS policies
- **Validation**: Request/response data validation
- **Error Handling**: Consistent error responses with auth context
- **Rate Limiting**: Protect against abuse (per user/IP)
- **Logging**: Request/response logging with user context

### Supabase Integration Layer
- **Database Client**: Supabase client configuration
- **Auth Client**: Supabase Auth service integration (email, Google, anonymous)
- **RLS Policies**: Row Level Security policy management
- **Session Management**: Automatic session handling

### Data Models
- **User Model**: Supabase Auth user structure (email, Google, anonymous)
- **Restaurant Model**: Restaurant entity with Gemini data
- **Favorite Model**: User-restaurant relationship with RLS
## 7. Android App Integration

### Authentication Integration Required
- **Add Supabase Auth SDK** to Android project
- **Add Google Sign-In** integration (primary login method)
- **Add "Continue as Guest"** option for anonymous access
- **Session management** handled automatically by Supabase
- **No signup/login forms needed** (simplified UI)

### API Integration Points
- **Replace hardcoded restaurant data** with authenticated API calls
- **Session-based authentication** (no manual token management)
- **Implement network layer** for HTTP requests with auth
- **Add offline caching** for better UX
- **Handle authentication errors** and session expiration

### User Experience Enhancements
- **Two simple login options**: Google Sign-In or Continue as Guest
- **Seamless guest-to-Google** account transition
- **Persistent favorites** across app sessions (for Google users)
- **Login state management** and persistence
- **Logout functionality** with data cleanup

### Data Mapping Strategy
- **Map backend restaurant objects** to existing Android model classes
- **Ensure rating breakdown data** for progress bars
- **Maintain image URL compatibility**
- **Preserve navigation data structure**
- **Handle user-specific data** (favorites only)
## 8. Deployment and Infrastructure

### Hosting Architecture
- **Supabase**: Database, Authentication, and Storage hosting
- **Railway/Render**: Express.js API server deployment
- **CDN**: Optional for static assets and images

### Environment Configuration
- **Supabase Project URL** and API keys
- **Supabase Service Role key** for admin operations
- **OperRouter API keys** for Gemini access
- **Google OAuth credentials** for Google Sign-In
- **CORS settings** for Android app
- **Environment-specific configurations** (dev/staging/prod)

### Supabase Configuration
- **Database setup** with RLS policies
- **Authentication providers**: Email/password, Google OAuth, Anonymous
- **API key management** and security
- **Backup and recovery** settings
- **Usage monitoring** and alerts

### Monitoring and Maintenance
- **Health check endpoints** for Express API
- **Supabase dashboard** monitoring
- **API response time** tracking
- **Authentication success/failure** rates
- **Error rate monitoring** with user context
- **Gemini API usage** tracking
- **Database performance** monitoring
## 9. Implementation Timeline

### Phase 1: Supabase Setup and Authentication (Week 1)
- **Set up Supabase project** and database schema
- **Configure Supabase Auth** with email/password, Google OAuth, and anonymous users
- **Set up Row Level Security (RLS)** policies
- **Create Node.js/Express project** with Supabase integration
- **Implement session-based authentication** middleware
- **Test authentication flows** (signup, login, Google, guest, logout)

### Phase 2: Core Backend Services (Week 2)
- **Integrate OperRouter** for Gemini 2.0 access
- **Develop restaurant search functionality** with user context
- **Implement response parsing** and validation
- **Add fallback mechanisms** and error handling
- **Create restaurant and favorites services** with RLS

### Phase 3: API Development and Integration (Week 3)
- **Build all required REST endpoints** with session authentication
- **Implement protected favorites management**
- **Add comprehensive data validation** and error handling
- **Test API compatibility** with Android app requirements
- **Implement user transition** from anonymous to registered

### Phase 4: Android Integration and Deployment (Week 4)
- **Integrate Supabase Auth SDK** in Android app
- **Implement Google Sign-In** and "Continue as Guest" buttons
- **Deploy Express API** to Railway/Render
- **Configure production Supabase** environment
- **Perform end-to-end testing** with simplified authentication flows
- **Optimize performance** and add comprehensive monitoring
## 10. Security and Performance Considerations

### Security Measures
- **Session-based authentication** (simplified, no JWT complexity)
- **Row Level Security (RLS)** policies in Supabase
- **Input validation** and sanitization
- **Rate limiting** on API endpoints (per user and IP)
- **Secure Supabase API key** management
- **Environment variable** protection
- **CORS configuration** for Android app
- **Google OAuth security** best practices

### Performance Optimization
- **Supabase connection pooling** and optimization
- **Database query optimization** with proper indexes
- **Gemini response caching** to reduce API calls
- **Session caching** strategies
- **Gzip compression** for API responses
- **Pagination** for large result sets
- **Efficient RLS policy** design

### Scalability Planning
- **Stateless API design** for horizontal scaling
- **Supabase auto-scaling** database features
- **Caching strategy** for frequently accessed data
- **Load balancing** considerations for future growth
- **Authentication service scaling** with Supabase

## 11. Supabase-Specific Implementation Details

### Database Setup
- **Create tables** with proper foreign key relationships
- **Implement RLS policies** for user data isolation
- **Set up database triggers** for audit trails
- **Configure automatic backups** and point-in-time recovery

### Authentication Configuration
- **Configure Google OAuth** provider (primary method)
- **Configure anonymous user** settings
- **Set up session management** (simplified approach)
- **Implement user metadata** for app-specific data
- **Disable email/password** authentication (not needed)

### Security Policies (RLS)
- **users table**: Users can only read/update their own data
- **user_favorites**: Users can only access their own favorites
- **restaurants**: Public read access, admin write access

### API Integration Patterns
- **Use Supabase client** for direct database operations
- **Implement service layer** for complex business logic
- **Handle Supabase errors** and edge cases gracefully
- **Optimize queries** with proper indexing and joins

---

This comprehensive plan provides a **robust foundation** for building a secure, scalable backend service that leverages Supabase's powerful features while maintaining **simplicity** and **seamless integration** with your existing Android app. The focus on core features (authentication, search, favorites) ensures a **streamlined development process** while adding sophisticated AI-driven restaurant search capabilities.