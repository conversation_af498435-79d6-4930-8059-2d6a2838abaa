require('dotenv').config();
const { supabase } = require('./src/config/supabase');

/**
 * Test script to verify Google OAuth authentication
 * This simulates what your Android app is doing
 */
async function testGoogleAuth() {
  console.log('🧪 Testing Google OAuth Authentication...\n');

  // Test with a real ID token from your Android app
  // Replace this with the actual ID token from your Android app logs
  const realIdToken = process.argv[2] || 'your-real-id-token-from-android-app';

  if (realIdToken === 'your-real-id-token-from-android-app') {
    console.log('💡 To test with a real token, run: node test-google-auth.js "your-actual-id-token"');
    console.log('You can get the ID token from your Android app logs\n');
  }
  
  console.log('📋 Configuration Check:');
  console.log(`Supabase URL: ${process.env.SUPABASE_URL}`);
  console.log(`Google Client ID: ${process.env.GOOGLE_CLIENT_ID}`);
  console.log(`Android Client ID matches: ${process.env.GOOGLE_CLIENT_ID.includes('845950786787-2eh1vbcsb896jmst21q3r2ihnap2ii5i')}`);
  console.log(`Note: Android apps don't typically need client secrets for Google OAuth\n`);

  if (process.env.GOOGLE_CLIENT_ID === 'your-google-client-id') {
    console.log('❌ Google OAuth not configured in .env file');
    console.log('Please update GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in your .env file\n');
    return;
  }

  try {
    console.log('🔐 Testing Google ID Token authentication...');
    
    // This is the same call your server makes
    const { data, error } = await supabase.auth.signInWithIdToken({
      provider: 'google',
      token: realIdToken
    });

    if (error) {
      console.log('❌ Authentication failed:');
      console.log(`Error: ${error.message}`);
      console.log(`Code: ${error.status || 'N/A'}`);
      
      if (error.message.includes('audience')) {
        console.log('\n💡 This suggests the Google OAuth provider is not configured in Supabase Dashboard');
        console.log('Please configure Google OAuth in your Supabase Dashboard with your Android app\'s client ID');
      }
    } else {
      console.log('✅ Authentication successful!');
      console.log(`User ID: ${data.user?.id}`);
      console.log(`Email: ${data.user?.email}`);
      console.log(`Provider: ${data.user?.app_metadata?.provider}`);
    }
  } catch (error) {
    console.log('❌ Test failed with error:', error.message);
  }
}

/**
 * Test the server endpoint directly
 */
async function testServerEndpoint() {
  console.log('\n🌐 Testing server endpoint...');
  
  const axios = require('axios');
  const serverUrl = `http://localhost:${process.env.PORT || 3000}`;
  
  try {
    // Test health endpoint first
    const healthResponse = await axios.get(`${serverUrl}/health`);
    console.log('✅ Server is running');
    console.log(`Health check: ${healthResponse.data.status}`);
    
    // Test auth endpoint with mock data
    const authResponse = await axios.post(`${serverUrl}/api/auth/google`, {
      idToken: 'mock-token-for-testing'
    });
    
    console.log('Auth endpoint response:', authResponse.data);
  } catch (error) {
    if (error.response) {
      console.log(`❌ Server responded with error: ${error.response.status}`);
      console.log(`Error message: ${error.response.data?.message || error.response.data?.error}`);
    } else {
      console.log(`❌ Could not connect to server: ${error.message}`);
      console.log('Make sure your server is running with: npm run dev');
    }
  }
}

// Run tests
async function runTests() {
  await testGoogleAuth();
  await testServerEndpoint();
  
  console.log('\n📚 Next Steps:');
  console.log('1. Configure Google OAuth in Supabase Dashboard');
  console.log('2. Update .env file with correct Google credentials');
  console.log('3. Test with a real ID token from your Android app');
  console.log('4. Verify the authentication flow works end-to-end');
}

runTests().catch(console.error);
