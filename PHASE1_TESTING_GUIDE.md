# 🧪 Foodie Backend - Phase 1 Testing Guide

## 📋 **Testing Scope Overview**

**✅ Phase 1 Testing (Current Focus):**
- AI Integration Testing (Gemini 2.0 via OpenRouter)
- Restaurant Data Structure and Quality
- Basic API Endpoints (Non-authenticated)
- Error Handling and Fallback Systems

**⏳ Phase 2 Testing (Future):**
- User Authentication (Google OAuth + Guest Users)
- Protected Endpoints (Favorites Management)
- Session Management

### **1. Restaurant Search Endpoint (AI Integration)**

**Purpose**: Test Gemini 2.0 AI integration and restaurant data structure

#### **Example 1_1: Location-Specific Search**
```bash
curl -X GET "http://localhost:3000/api/restaurants/search?q=sushi&location=toronto"
```
**✅ Expected Success Response Structure:**
```json
{
  "success": true,
  "restaurants": [
    {
      "name": "Terroni Adelaide",
      "cuisine_type": "Southern Italian",
      "rating": 4.2,
      "address": "57 Adelaide St E, Toronto, ON M5C 1K9",
      "phone": "(*************",
      "description": "A Toronto institution, Terroni Adelaide serves authentic Southern Italian cuisine in a bustling and vibrant atmosphere. Known for its pizza and pasta, this spot uses traditional ingredients and techniques to create a genuinely Italian experience.",
      "hours": {
        "monday": "11:30 AM - 11:00 PM",
        "tuesday": "11:30 AM - 11:00 PM",
        "wednesday": "11:30 AM - 11:00 PM",
        "thursday": "11:30 AM - 11:00 PM",
        "friday": "11:30 AM - 12:00 AM",
        "saturday": "11:30 AM - 12:00 AM",
        "sunday": "11:30 AM - 10:00 PM"
      }
    }
  ],
  "count": 6
}
```

**🔍 Data Structure Validation Checklist:**
- ✅ `name`: String, restaurant name
- ✅ `cuisine_type`: String, type of cuisine
- ✅ `rating`: Number, 3.0-5.0 range
- ✅ `address`: String, full address
- ✅ `phone`: String, formatted phone number
- ✅ `description`: String, 1-2 compelling sentences
- ✅ `hours`: Object, operating hours for all days

#### **Example 1_2: Missing Query Parameter**
```bash
curl -X GET "http://localhost:3000/api/restaurants/search"
```

**❌ Expected Error Response:**
```json
{
  "error": "Missing required parameter",
  "message": "Query parameter \"q\" is required"
}
```

#### **Example 1_3: Empty Query Parameter**
```bash
curl -X GET "http://localhost:3000/api/restaurants/search?q="
```

---


### **2. Add Restaurant to Favorites Endpoint**

**Purpose**: Test adding restaurants to user favorites and database storage

```bash
# Add the restaurant to favorites using the complete restaurant data
curl -X POST "http://localhost:3000/api/favorites" \
  -H "Content-Type: application/json" \
  -d '{
    "restaurant_data": {
      "name": "Terroni Adelaide",
      "cuisine_type": "Southern Italian",
      "rating": 4.2,
      "address": "57 Adelaide St E, Toronto, ON M5C 1K9",
      "phone": "(*************",
      "description": "A Toronto institution, Terroni Adelaide serves authentic Southern Italian cuisine in a bustling and vibrant atmosphere. Known for its pizza and pasta, this spot uses traditional ingredients and techniques to create a genuinely Italian experience.",
      "hours": {
        "monday": "11:30 AM - 11:00 PM",
        "tuesday": "11:30 AM - 11:00 PM",
        "wednesday": "11:30 AM - 11:00 PM",
        "thursday": "11:30 AM - 11:00 PM",
        "friday": "11:30 AM - 12:00 AM",
        "saturday": "11:30 AM - 12:00 AM",
        "sunday": "11:30 AM - 10:00 PM"
      }
    }
  }'
```

**✅ Expected Success Response:**
```json
{
  "success": true,
  "message": "Restaurant added to favorites",
  "favorite_id": "uuid-123",
  "restaurant_id": "uuid-456"
}
```

**🔍 Favorites Data Flow Validation:**
- ✅ Restaurant data is stored in `restaurants` table (only when favorited)
- ✅ User-restaurant relationship created in `user_favorites` table
- ✅ Restaurant gets unique ID for future reference
- ✅ Complete restaurant data preserved (name, cuisine, rating, address, phone, description, hours)



### **3. Get User Favorites Endpoint**

**Purpose**: Test retrieving user's favorite restaurants

```bash
# Get user's favorite restaurants (requires auth in Phase 2)
curl -X GET "http://localhost:3000/api/favorites"
```

**✅ Expected Success Response:**
```json
{
  "success": true,
  "favorites": [
    {
      "id": "favorite-uuid-123",
      "restaurant": {
        "id": "restaurant-uuid-456",
        "name": "Terroni Adelaide",
        "cuisine_type": "Southern Italian",
        "rating": 4.2,
        "address": "57 Adelaide St E, Toronto, ON M5C 1K9",
        "phone": "(*************",
        "description": "A Toronto institution serving authentic Southern Italian cuisine in a bustling and vibrant atmosphere. Known for its pizza and pasta, this spot uses traditional ingredients and techniques to create a genuinely Italian experience.",
        "hours": {
          "monday": "11:30 AM - 11:00 PM",
          "tuesday": "11:30 AM - 11:00 PM",
          "wednesday": "11:30 AM - 11:00 PM",
          "thursday": "11:30 AM - 11:00 PM",
          "friday": "11:30 AM - 12:00 AM",
          "saturday": "11:30 AM - 12:00 AM",
          "sunday": "11:30 AM - 10:00 PM"
        }
      },
      "created_at": "2025-08-03T14:30:00.000Z"
    }
  ],
  "count": 1
}
```

### **4. Remove from Favorites Endpoint**

**Purpose**: Test removing restaurants from user favorites

```bash
# Remove restaurant from favorites using favorite_id from previous response
curl -X DELETE "http://localhost:3000/api/favorites/favorite-uuid-123"
```

**✅ Expected Success Response:**
```json
{
  "success": true,
  "message": "Restaurant removed from favorites"
}
```

**❌ Expected Error Response (Favorite not found):**
```json
{
  "error": "Favorite not found",
  "message": "The specified favorite could not be found"
}
```

### **5. Trending Restaurants Endpoint (Updated)**

**Purpose**: Test trending restaurants based on favorites (most favorited restaurants)

```bash
# Get trending restaurants (now based on favorite count)
curl -X GET "http://localhost:3000/api/restaurants/trending"
```

**✅ Expected Success Response:**
```json
{
  "success": true,
  "restaurants": [
    {
      "id": "restaurant-uuid-456",
      "name": "Terroni Adelaide",
      "cuisine_type": "Southern Italian",
      "rating": 4.2,
      "address": "57 Adelaide St E, Toronto, ON M5C 1K9",
      "phone": "(*************",
      "description": "A Toronto institution serving authentic Southern Italian cuisine...",
      "hours": {
        "monday": "11:30 AM - 11:00 PM",
        "tuesday": "11:30 AM - 11:00 PM",
        "wednesday": "11:30 AM - 11:00 PM",
        "thursday": "11:30 AM - 11:00 PM",
        "friday": "11:30 AM - 12:00 AM",
        "saturday": "11:30 AM - 12:00 AM",
        "sunday": "11:30 AM - 10:00 PM"
      },
      "favorite_count": 5,
      "created_at": "2025-08-03T14:30:00.000Z"
    }
  ],
  "count": 1
}
```

**📊 Note**: Trending restaurants are now based on actual user favorites, making the data more meaningful and engagement-driven.

---

## 📱 **Android Device Testing Guide**

**Purpose**: Test the complete flow from Android app perspective

### **Android Testing Flow:**

#### **Step 1: Restaurant Search (Android → Backend)**
```kotlin
// Android API call
val searchResponse = apiService.searchRestaurants("italian", "toronto")

// Expected response structure (same as curl test)
data class RestaurantSearchResponse(
    val success: Boolean,
    val restaurants: List<Restaurant>,
    val count: Int
)

data class Restaurant(
    val name: String,
    val cuisine_type: String,
    val rating: Double,
    val address: String,
    val phone: String,
    val description: String,
    val hours: Map<String, String>
)
```

#### **Step 2: Add to Favorites (Android → Backend)**
```kotlin
// When user taps "Add to Favorites" button
val favoriteRequest = FavoriteRequest(
    restaurant_data = selectedRestaurant
)

val favoriteResponse = apiService.addToFavorites(favoriteRequest)

// Expected response
data class FavoriteResponse(
    val success: Boolean,
    val message: String,
    val favorite_id: String,
    val restaurant_id: String
)
```

#### **Step 3: View Favorites (Android → Backend)**
```kotlin
// Get user's favorites
val favoritesResponse = apiService.getFavorites()

// Expected response
data class FavoritesResponse(
    val success: Boolean,
    val favorites: List<FavoriteItem>,
    val count: Int
)

data class FavoriteItem(
    val id: String,
    val restaurant: Restaurant,
    val created_at: String
)
```

### **Android Testing Checklist:**

#### **🔍 Restaurant Search Testing:**
- [ ] Search returns 3-10 restaurants
- [ ] All restaurant fields are populated (name, cuisine_type, rating, address, phone, description, hours)
- [ ] Descriptions are meaningful and engaging
- [ ] No `image_urls` field in response
- [ ] Handle 429 rate limit errors gracefully
- [ ] Show appropriate error messages for rate limits

#### **❤️ Favorites Testing:**
- [ ] Can add restaurant to favorites from search results
- [ ] Favorite addition returns success response with IDs
- [ ] Can view list of favorite restaurants
- [ ] Favorite restaurants show complete data
- [ ] Can remove restaurants from favorites
- [ ] Favorites persist between app sessions

#### **📊 Data Flow Testing:**
- [ ] Search results are NOT cached in database (only stored when favorited)
- [ ] Favoriting a restaurant stores it in database
- [ ] Trending restaurants show most favorited items
- [ ] Database relationships work correctly (restaurants ↔ user_favorites)

---

## 🎯 **Phase 1 Testing Summary**

### **✅ Core Functionality Tests**
- [ ] Restaurant search with AI integration
- [ ] Rate limit handling (429 errors)
- [ ] Mock data fallback for other errors
- [ ] Restaurant data structure validation
- [ ] Database caching verification
- [ ] Favorites endpoints structure (authentication pending Phase 2)

### **🚀 Ready for Phase 2**
Once Phase 1 tests pass, proceed to Phase 2 for:
- User authentication (Google OAuth + Guest users)
- Protected favorites endpoints
- Session management testing
